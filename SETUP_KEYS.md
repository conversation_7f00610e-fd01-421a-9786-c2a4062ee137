# 🔑 إعداد مفاتيح API - Gaming News Agent

## ✅ تم حل مشكلة Hugging Face!

تم إزالة جميع مفاتيح API من الكود واستبدالها بمتغيرات البيئة. الآن يمكنك رفع المشروع بأمان إلى Hugging Face Spaces.

## 🚀 خطوات الإعداد السريع

### 1. للاستخدام المحلي:
```bash
# انسخ ملف المثال
cp .env.example .env

# عدل الملف وأضف مفاتيحك الحقيقية
nano .env
```

### 2. لـ Hugging Face Spaces:

اذهب إلى **Settings** → **Repository secrets** وأضف:

#### مفاتيح أساسية (مطلوبة):
```
HF_TOKEN=hf_your_token_here
GEMINI_API_KEY=your_gemini_key_here
```

#### مفاتيح اختيارية (للمميزات الإضافية):
```
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
YOUTUBE_API_KEY=your_youtube_key
GOOGLE_SEARCH_KEYS=key1,key2,key3
NEWSAPI_KEY=your_newsapi_key
PEXELS_API_KEY=your_pexels_key
TAVILY_API_KEY_1=your_tavily_key
```

## 📋 قائمة المفاتيح المدعومة

### Google APIs:
- `GEMINI_API_KEY` - مطلوب للذكاء الاصطناعي
- `GOOGLE_SEARCH_ENGINE_ID` - للبحث المخصص
- `YOUTUBE_API_KEY` - لتحليل YouTube
- `GOOGLE_SEARCH_KEYS` - مفاتيح متعددة (مفصولة بفواصل)
- `GOOGLE_API_KEYS_LIST` - قائمة إضافية للمفاتيح
- `PAGESPEED_INSIGHTS_API_KEY` - لتحليل الأداء

### Hugging Face:
- `HF_TOKEN` - مطلوب للوصول لـ APIs

### أخبار:
- `NEWSAPI_KEY` - NewsAPI.org
- `NEWSDATA_KEY` - NewsData.io
- `THENEWSAPI_KEY` - TheNewsAPI.com
- `GNEWS_KEY` - GNews.io

### بحث:
- `TAVILY_API_KEY_1`, `TAVILY_API_KEY_2` - Tavily AI
- `SEARCH1API_KEY_1`, `SEARCH1API_KEY_2` - Search1API
- `BRAVE_SEARCH_KEY` - Brave Search
- `SERPAPI_KEY` - SerpAPI
- `RAPIDAPI_KEY` - RapidAPI

### صور:
- `PEXELS_API_KEY` - Pexels
- `PIXABAY_API_KEY` - Pixabay
- `UNSPLASH_ACCESS_KEY` - Unsplash
- `FREEPIK_API_KEY` - Freepik
- `FLUXAI_API_KEY` - FluxAI

### ألعاب:
- `TWITCH_CLIENT_ID`, `TWITCH_CLIENT_SECRET` - Twitch/IGDB
- `RAWG_API_KEY` - RAWG.io
- `STEAM_API_KEY` - Steam

## 🔒 أمان المفاتيح

### ✅ ما تم عمله:
- إزالة جميع المفاتيح من الكود
- استخدام `os.getenv()` لجميع المفاتيح
- إضافة `.gitignore` شامل
- حذف ملفات `__pycache__`

### ⚠️ تذكير مهم:
- لا تضع مفاتيح حقيقية في الكود أبداً
- استخدم Repository Secrets في Hugging Face
- لا تشارك ملف `.env` مع أحد
- احدث المفاتيح بانتظام

## 🧪 اختبار الإعداد

```python
# أضف هذا الكود مؤقتاً للاختبار
import os
print("🔑 فحص المفاتيح:")
print(f"HF_TOKEN: {'✅' if os.getenv('HF_TOKEN') else '❌'}")
print(f"GEMINI_API_KEY: {'✅' if os.getenv('GEMINI_API_KEY') else '❌'}")
```

## 📞 الدعم

إذا واجهت مشاكل:
1. تأكد من صحة أسماء المتغيرات
2. تحقق من عدم وجود مسافات إضافية
3. راجع logs التطبيق للأخطاء

---

**الآن يمكنك رفع المشروع بأمان إلى Hugging Face! 🎉**

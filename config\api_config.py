# إعدادات APIs المتقدمة
import os
from typing import Dict, Optional

class APIConfig:
    """إعدادات APIs المتقدمة"""
    
    # APIs أساسية (موجودة)
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY', '')
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')
    YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY', '')

    # مجموعة مفاتيح Gemini متعددة للتناوب والاحتياط
    GEMINI_API_KEYS_POOL = [
        os.getenv('GEMINI_API_KEY_1', ''),
        os.getenv('GEMINI_API_KEY_2', ''),
        os.getenv('GEMINI_API_KEY_3', ''),
        os.getenv('GEMINI_API_KEY_4', ''),
        os.getenv('GEMINI_API_KEY_5', ''),
        os.getenv('GEMINI_API_KEY_6', ''),
        os.getenv('GEMINI_API_KEY_7', ''),
        os.getenv('GEMINI_API_KEY_8', ''),
        os.getenv('GEMINI_API_KEY_9', ''),
        os.getenv('GEMINI_API_KEY_10', ''),
    ]

    # إزالة المفاتيح الفارغة
    GEMINI_API_KEYS_POOL = [key for key in GEMINI_API_KEYS_POOL if key.strip()]

    # إضافة المفتاح الأساسي إذا لم يكن في المجموعة
    if GEMINI_API_KEY and GEMINI_API_KEY not in GEMINI_API_KEYS_POOL:
        GEMINI_API_KEYS_POOL.insert(0, GEMINI_API_KEY)

    # APIs متقدمة للـ SEO والتحليل
    GOOGLE_SEARCH_CONSOLE_KEY = os.getenv('GOOGLE_SEARCH_CONSOLE_KEY', '')
    GOOGLE_ANALYTICS_KEY = os.getenv('GOOGLE_ANALYTICS_KEY', '')
    GOOGLE_PAGESPEED_KEY = os.getenv('GOOGLE_PAGESPEED_KEY', '')

    # Microsoft Clarity (مجاني)
    MICROSOFT_CLARITY_ID = os.getenv('MICROSOFT_CLARITY_ID', '')

    # Ubersuggest (مجاني جزئياً)
    UBERSUGGEST_API_KEY = os.getenv('UBERSUGGEST_API_KEY', '')
    
    # APIs أدوات SEO المدفوعة
    SEMRUSH_API_KEY = os.getenv('SEMRUSH_API_KEY', '')
    AHREFS_API_KEY = os.getenv('AHREFS_API_KEY', '')
    MOZ_API_KEY = os.getenv('MOZ_API_KEY', '')
    
    # APIs التحليل والمراقبة
    HOTJAR_API_KEY = os.getenv('HOTJAR_API_KEY', '')
    MIXPANEL_API_KEY = os.getenv('MIXPANEL_API_KEY', '')
    FACEBOOK_PIXEL_ID = os.getenv('FACEBOOK_PIXEL_ID', '')
    
    # APIs وسائل التواصل الاجتماعي
    TWITTER_API_KEY = os.getenv('TWITTER_API_KEY', '')
    TWITTER_API_SECRET = os.getenv('TWITTER_API_SECRET', '')
    FACEBOOK_API_KEY = os.getenv('FACEBOOK_API_KEY', '')
    INSTAGRAM_API_KEY = os.getenv('INSTAGRAM_API_KEY', '')
    
    # APIs المحتوى والتحليل
    BUZZSUMO_API_KEY = os.getenv('BUZZSUMO_API_KEY', '')
    CLEARBIT_API_KEY = os.getenv('CLEARBIT_API_KEY', '')
    HUNTER_API_KEY = os.getenv('HUNTER_API_KEY', '')
    
    # إعدادات Core Web Vitals
    CORE_WEB_VITALS_CONFIG = {
        'lcp_threshold_good': 2.5,      # ثانية
        'lcp_threshold_poor': 4.0,      # ثانية
        'fid_threshold_good': 100,      # مللي ثانية
        'fid_threshold_poor': 300,      # مللي ثانية
        'cls_threshold_good': 0.1,      # نقاط
        'cls_threshold_poor': 0.25,     # نقاط
        'monitoring_interval': 3600,    # ثانية (ساعة واحدة)
        'alert_threshold': 60           # نقاط أقل من 60 = تنبيه
    }
    
    # إعدادات بحث الكلمات المفتاحية
    KEYWORD_RESEARCH_CONFIG = {
        'max_keywords_per_request': 100,
        'min_search_volume': 100,
        'max_keyword_difficulty': 80,
        'target_languages': ['ar', 'en'],
        'competitor_domains': [
            'gamespot.com',
            'ign.com',
            'polygon.com',
            'kotaku.com',
            'eurogamer.net',
            'pcgamer.com',
            'gamesradar.com'
        ]
    }
    
    # إعدادات مراقبة الأداء
    PERFORMANCE_MONITORING_CONFIG = {
        'monitoring_enabled': True,
        'monitoring_interval': 3600,    # ساعة واحدة
        'alert_email': '',              # بريد إلكتروني للتنبيهات
        'telegram_bot_token': '',       # توكن بوت تيليجرام للتنبيهات
        'telegram_chat_id': '',         # معرف المحادثة
        'website_url': 'https://your-gaming-website.com',  # URL موقعك
        'important_pages': [
            '/latest-news',
            '/game-reviews',
            '/guides',
            '/trending'
        ]
    }
    
    # حدود معدل الطلبات
    RATE_LIMITS = {
        'google_apis': {
            'requests_per_minute': 100,
            'requests_per_day': 10000
        },
        'semrush': {
            'requests_per_minute': 10,
            'requests_per_day': 1000
        },
        'ahrefs': {
            'requests_per_minute': 20,
            'requests_per_day': 2000
        },
        'social_apis': {
            'requests_per_minute': 50,
            'requests_per_day': 5000
        },
        'analytics_apis': {
            'requests_per_minute': 30,
            'requests_per_day': 3000
        }
    }
    
    # URLs APIs
    API_ENDPOINTS = {
        'google_pagespeed': 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed',
        'google_search_console': 'https://www.googleapis.com/webmasters/v3',
        'google_analytics': 'https://analyticsreporting.googleapis.com/v4/reports:batchGet',
        'semrush_overview': 'https://api.semrush.com/',
        'ahrefs_overview': 'https://apiv2.ahrefs.com',
        'buzzsumo_content': 'https://api.buzzsumo.com/search/content',
        'twitter_api': 'https://api.twitter.com/2',
        'facebook_graph': 'https://graph.facebook.com/v18.0'
    }
    
    @classmethod
    def get_api_key(cls, api_name: str) -> Optional[str]:
        """الحصول على مفتاح API"""
        api_keys = {
            'google': cls.GOOGLE_API_KEY,
            'gemini': cls.GEMINI_API_KEY,
            'youtube': cls.YOUTUBE_API_KEY,
            'google_search_console': cls.GOOGLE_SEARCH_CONSOLE_KEY,
            'google_analytics': cls.GOOGLE_ANALYTICS_KEY,
            'google_pagespeed': cls.GOOGLE_PAGESPEED_KEY,
            'semrush': cls.SEMRUSH_API_KEY,
            'ahrefs': cls.AHREFS_API_KEY,
            'moz': cls.MOZ_API_KEY,
            'hotjar': cls.HOTJAR_API_KEY,
            'mixpanel': cls.MIXPANEL_API_KEY,
            'twitter': cls.TWITTER_API_KEY,
            'facebook': cls.FACEBOOK_API_KEY,
            'instagram': cls.INSTAGRAM_API_KEY,
            'buzzsumo': cls.BUZZSUMO_API_KEY,
            'clearbit': cls.CLEARBIT_API_KEY,
            'hunter': cls.HUNTER_API_KEY
        }
        
        return api_keys.get(api_name)
    
    @classmethod
    def is_api_available(cls, api_name: str) -> bool:
        """فحص توفر API"""
        api_key = cls.get_api_key(api_name)
        return bool(api_key and api_key.strip())
    
    @classmethod
    def get_available_apis(cls) -> Dict[str, bool]:
        """الحصول على قائمة APIs المتوفرة"""
        apis = [
            'google', 'gemini', 'youtube', 'google_search_console',
            'google_analytics', 'google_pagespeed', 'semrush', 'ahrefs',
            'moz', 'hotjar', 'mixpanel', 'twitter', 'facebook',
            'instagram', 'buzzsumo', 'clearbit', 'hunter'
        ]
        
        return {api: cls.is_api_available(api) for api in apis}
    
    @classmethod
    def get_rate_limit(cls, api_category: str) -> Dict:
        """الحصول على حدود معدل الطلبات"""
        return cls.RATE_LIMITS.get(api_category, {
            'requests_per_minute': 10,
            'requests_per_day': 1000
        })
    
    @classmethod
    def get_endpoint_url(cls, api_name: str) -> Optional[str]:
        """الحصول على URL نقطة النهاية"""
        return cls.API_ENDPOINTS.get(api_name)
    
    @classmethod
    def validate_configuration(cls) -> Dict:
        """التحقق من صحة الإعدادات"""
        validation_results = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'available_apis': 0,
            'total_apis': 0
        }
        
        # فحص APIs الأساسية
        essential_apis = ['google', 'gemini']
        for api in essential_apis:
            cls.total_apis += 1
            if cls.is_api_available(api):
                validation_results['available_apis'] += 1
            else:
                validation_results['errors'].append(f"مفتاح {api} API مطلوب ولكنه غير متوفر")
                validation_results['valid'] = False
        
        # فحص APIs الاختيارية
        optional_apis = ['semrush', 'ahrefs', 'google_pagespeed']
        for api in optional_apis:
            validation_results['total_apis'] += 1
            if cls.is_api_available(api):
                validation_results['available_apis'] += 1
            else:
                validation_results['warnings'].append(f"مفتاح {api} API غير متوفر - سيتم استخدام بيانات محاكاة")
        
        # فحص إعدادات المراقبة
        if not cls.PERFORMANCE_MONITORING_CONFIG['website_url'].startswith('http'):
            validation_results['warnings'].append("URL الموقع غير صحيح في إعدادات المراقبة")
        
        return validation_results

# دالة مساعدة لإعداد متغيرات البيئة
def setup_environment_variables():
    """إعداد متغيرات البيئة للـ APIs"""
    env_template = """
# APIs أساسية
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
YOUTUBE_API_KEY=your_youtube_api_key_here

# APIs متقدمة للـ SEO
GOOGLE_SEARCH_CONSOLE_KEY=your_search_console_key_here
GOOGLE_ANALYTICS_KEY=your_analytics_key_here
GOOGLE_PAGESPEED_KEY=your_pagespeed_key_here

# APIs أدوات SEO المدفوعة
SEMRUSH_API_KEY=your_semrush_key_here
AHREFS_API_KEY=your_ahrefs_key_here
MOZ_API_KEY=your_moz_key_here

# APIs التحليل والمراقبة
HOTJAR_API_KEY=your_hotjar_key_here
MIXPANEL_API_KEY=your_mixpanel_key_here
FACEBOOK_PIXEL_ID=your_facebook_pixel_id_here

# APIs وسائل التواصل
TWITTER_API_KEY=your_twitter_key_here
TWITTER_API_SECRET=your_twitter_secret_here
FACEBOOK_API_KEY=your_facebook_key_here
INSTAGRAM_API_KEY=your_instagram_key_here

# APIs المحتوى والتحليل
BUZZSUMO_API_KEY=your_buzzsumo_key_here
CLEARBIT_API_KEY=your_clearbit_key_here
HUNTER_API_KEY=your_hunter_key_here
"""
    
    # إنشاء ملف .env إذا لم يكن موجوداً
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_template)
        print("✅ تم إنشاء ملف .env - يرجى إضافة مفاتيح APIs الخاصة بك")
    else:
        print("⚠️ ملف .env موجود بالفعل")

def print_api_status():
    """طباعة حالة APIs"""
    print("📊 حالة APIs المتوفرة:")
    print("="*50)
    
    available_apis = APIConfig.get_available_apis()
    
    for api_name, is_available in available_apis.items():
        status = "✅ متوفر" if is_available else "❌ غير متوفر"
        print(f"  • {api_name}: {status}")
    
    total_available = sum(available_apis.values())
    total_apis = len(available_apis)
    
    print(f"\n📈 الإحصائيات:")
    print(f"  • APIs متوفرة: {total_available}/{total_apis}")
    print(f"  • نسبة التوفر: {(total_available/total_apis)*100:.1f}%")
    
    # التحقق من الإعدادات
    validation = APIConfig.validate_configuration()
    
    if validation['errors']:
        print(f"\n❌ أخطاء:")
        for error in validation['errors']:
            print(f"  • {error}")
    
    if validation['warnings']:
        print(f"\n⚠️ تحذيرات:")
        for warning in validation['warnings']:
            print(f"  • {warning}")

if __name__ == "__main__":
    # إعداد متغيرات البيئة
    setup_environment_variables()
    
    # طباعة حالة APIs
    print_api_status()

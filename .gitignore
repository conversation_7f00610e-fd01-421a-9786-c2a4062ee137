# ملفات متغيرات البيئة والمفاتيح الحساسة
.env
.env.local
.env.production
.env.staging
*.env

# ملفات المفاتيح والشهادات
client_secret.json
*.pem
*.key
*.crt
*.p12
*.pfx

# مجلدات البيانات والسجلات
logs/
cache/
data/
temp/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# ملفات النظام
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات النسخ الاحتياطية
*.bak
*.backup
*.old
*.orig

# ملفات قواعد البيانات المحلية
*.db
*.sqlite
*.sqlite3

# ملفات الصور المؤقتة
images/temp/
cache/images/
manual_images/

# ملفات الصوت المؤقتة
*.mp3
*.wav
*.m4a
audio_temp/

# ملفات التكوين المحلية
config/local_*.py
config/*_local.json

# ملفات Jupyter Notebook
.ipynb_checkpoints

# ملفات pytest
.pytest_cache/
.coverage

# ملفات mypy
.mypy_cache/
.dmypy.json
dmypy.json

# ملفات Pyre
.pyre/

# ملفات pyenv
.python-version

# ملفات pipenv
Pipfile.lock

# ملفات poetry
poetry.lock

# ملفات conda
environment.yml

# ملفات Docker
.dockerignore
Dockerfile.local

# ملفات النشر
deploy/
deployment/
.deploy/

# ملفات التجارب والاختبارات
experiments/
test_data/
sandbox/

# ملفات المراقبة
monitoring/
metrics/
analytics/

# ملفات الأمان
security/
secrets/
credentials/

# ملفات التوثيق المحلية
docs/build/
site/

# ملفات النماذج المدربة
models/
*.model
*.pkl
*.joblib

# ملفات البيانات الكبيرة
*.csv
*.json.gz
*.parquet
large_data/

# ملفات الشبكة
*.log
*.out
*.err

# ملفات التطوير
dev/
development/
local/

# ملفات المحاكاة
simulation/
mock/
fake_data/

# 🔧 حل مشكلة Hugging Face Secrets

## 🚨 المشكلة
```
Error: It appears that one or more of your files contain valid Hugging Face secrets, such as tokens or API keys.
Offending files: - config/__pycache__/settings.cpython-313.pyc
```

## ✅ الحل السريع

### الخطوة 1: حذف ملفات __pycache__
```bash
# في Windows PowerShell
Remove-Item -Recurse -Force "*\__pycache__"

# في Linux/Mac
find . -type d -name "__pycache__" -exec rm -rf {} +
```

### الخطوة 2: التأكد من .gitignore
تأكد من وجود هذه الأسطر في ملف `.gitignore`:
```
# ملفات Python المترجمة
__pycache__/
*/__pycache__/
*/*/__pycache__/
*/*/*/__pycache__/
*.pyc
*.pyo
*.pyd
```

### الخطوة 3: إعداد Repository Secrets في Hugging Face

بدلاً من وضع المفاتيح في الكود، استخدم Repository Secrets:

1. اذهب إلى Space الخاص بك في Hugging Face
2. انقر على **Settings** → **Repository secrets**
3. أضف المفاتيح التالية:

```
HF_TOKEN=hf_your_actual_token_here
GEMINI_API_KEY=your_gemini_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
```

### الخطوة 4: تعديل الكود لاستخدام متغيرات البيئة

في ملف `config/settings.py`:
```python
# بدلاً من:
HF_TOKEN = "*************************************"

# استخدم:
HF_TOKEN = os.getenv("HF_TOKEN", "")
```

## 🔄 خطوات الرفع الآمن

### 1. تنظيف المستودع
```bash
# حذف ملفات __pycache__
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +

# حذف ملفات أخرى حساسة
rm -rf logs/
rm -rf cache/
rm -rf data/
```

### 2. إضافة الملفات للـ git
```bash
git add .gitignore
git add -A
git status  # تأكد من عدم وجود ملفات __pycache__
```

### 3. الرفع
```bash
git commit -m "Fix: Remove sensitive data and add proper gitignore"
git push origin main
```

## 🛡️ منع المشكلة مستقبلاً

### 1. استخدم متغيرات البيئة دائماً
```python
# ✅ صحيح
api_key = os.getenv("API_KEY", "")

# ❌ خطأ
api_key = "actual_key_here"
```

### 2. أضف hook قبل الـ commit
إنشاء ملف `.git/hooks/pre-commit`:
```bash
#!/bin/bash
# فحص وجود مفاتيح حساسة
if grep -r "hf_" --include="*.py" .; then
    echo "❌ تم العثور على مفاتيح Hugging Face في الكود!"
    exit 1
fi
```

### 3. استخدم أدوات الفحص
```bash
# تثبيت detect-secrets
pip install detect-secrets

# فحص المستودع
detect-secrets scan --all-files
```

## 📋 قائمة فحص قبل الرفع

- [ ] حذف جميع ملفات `__pycache__`
- [ ] تحديث `.gitignore`
- [ ] استخدام `os.getenv()` للمفاتيح
- [ ] إضافة المفاتيح في Repository Secrets
- [ ] فحص عدم وجود مفاتيح في الكود
- [ ] اختبار التطبيق محلياً

## 🔍 فحص سريع

```bash
# فحص وجود مفاتيح Hugging Face
grep -r "hf_" --include="*.py" .

# فحص وجود ملفات __pycache__
find . -name "__pycache__" -type d

# فحص .gitignore
cat .gitignore | grep -E "(pycache|\.pyc)"
```

## 📞 إذا استمرت المشكلة

1. **احذف المستودع** وأنشئ واحد جديد
2. **انسخ الملفات** بدون مجلدات `__pycache__`
3. **تأكد من .gitignore** قبل أول commit
4. **استخدم Repository Secrets** فقط

---

**تذكير**: لا تضع أبداً مفاتيح APIs في الكود المرفوع علناً!

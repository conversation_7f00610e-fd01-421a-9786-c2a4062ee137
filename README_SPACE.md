---
title: Gaming News Agent
emoji: 🎮
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---

# 🎮 Gaming News Agent

وكيل ذكي متطور لجمع ونشر أخبار الألعاب باستخدام الذكاء الاصطناعي.

## ✨ المميزات

- 🤖 **ذكاء اصطناعي متقدم**: يستخدم Gemini AI لتحليل وإنشاء المحتوى
- 🔍 **بحث ذكي**: يجمع الأخبار من مصادر متعددة
- 🎥 **تحليل YouTube**: يحلل مقاطع الفيديو ويستخرج النصوص
- 🖼️ **إنشاء الصور**: ينشئ صور مناسبة للمقالات
- 📱 **واجهة ويب**: لوحة تحكم شاملة وسهلة الاستخدام
- 🔒 **آمان عالي**: حماية المفاتيح والبيانات الحساسة

## 🚀 كيفية الاستخدام

### 1. إعداد المفاتيح (للمطورين)

لاستخدام جميع المميزات، أضف المفاتيح التالية في **Repository Secrets**:

#### مفاتيح أساسية:
- `HF_TOKEN`: مفتاح Hugging Face ([احصل عليه هنا](https://huggingface.co/settings/tokens))
- `GEMINI_API_KEY`: مفتاح Google Gemini ([احصل عليه هنا](https://makersuite.google.com/app/apikey))

#### مفاتيح اختيارية:
- `GOOGLE_SEARCH_ENGINE_ID`: لبحث Google المخصص
- `YOUTUBE_API_KEY`: لتحليل YouTube
- `NEWSAPI_KEY`: لجمع الأخبار
- `PEXELS_API_KEY`: للصور المجانية

### 2. استخدام التطبيق

1. **فحص الحالة**: تحقق من حالة مفاتيح API في تبويب "حالة النظام"
2. **إنشاء المحتوى**: استخدم تبويب "إنشاء المحتوى" لإنشاء مقالات
3. **مراقبة السجلات**: تابع نشاط النظام في سجل الأحداث

## 🔧 التكوين المتقدم

### للمطورين:

```bash
# استنساخ المشروع
git clone https://huggingface.co/spaces/YOUR_USERNAME/gaming-news-agent
cd gaming-news-agent

# تثبيت المتطلبات
pip install -r requirements.txt

# إعداد متغيرات البيئة
cp .env.example .env
# عدل ملف .env وأضف مفاتيحك

# تشغيل التطبيق محلياً
python app.py
```

## 📁 هيكل المشروع

```
├── app.py                 # الواجهة الرئيسية
├── requirements.txt       # المتطلبات
├── config/               # ملفات التكوين
├── modules/              # الوحدات الأساسية
├── .env.example          # مثال متغيرات البيئة
└── README.md            # هذا الملف
```

## 🛡️ الأمان والخصوصية

- ✅ جميع المفاتيح محمية في متغيرات البيئة
- ✅ لا توجد بيانات حساسة في الكود
- ✅ تشفير البيانات المرسلة
- ✅ مراقبة استخدام APIs

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجل النشاط في التطبيق
2. تأكد من صحة مفاتيح API
3. راجع [الوثائق الكاملة](https://github.com/your-repo/gaming-news-agent)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**ملاحظة**: هذا التطبيق مصمم للاستخدام التعليمي والشخصي. تأكد من الامتثال لشروط استخدام جميع APIs المستخدمة.

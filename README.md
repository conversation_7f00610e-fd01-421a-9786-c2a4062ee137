# 🎮 Gaming News Agent - وكيل أخبار الألعاب الذكي

وكيل ذكي متطور لجمع ونشر أخبار الألعاب باستخدام الذكاء الاصطناعي، مصمم للعمل على Hugging Face Spaces.

## ✨ المميزات

- 🤖 **ذكاء اصطناعي متقدم**: يستخدم Gemini AI لتحليل وإنشاء المحتوى
- 🔍 **بحث ذكي**: يجمع الأخبار من مصادر متعددة
- 🎥 **تحليل YouTube**: يحلل مقاطع الفيديو ويستخرج النصوص
- 🖼️ **إنشاء الصور**: ينشئ صور مناسبة للمقالات
- 📱 **واجهة ويب**: لوحة تحكم شاملة
- 🔒 **آمان عالي**: حماية المفاتيح والبيانات الحساسة

## 🚀 الإعداد السريع لـ Hugging Face Spaces

### 1. إعد<PERSON> المفاتيح الآمن

**⚠️ هام جداً**: لا تضع المفاتيح مباشرة في الكود!

#### أ) في Hugging Face Spaces:
1. اذهب إلى إعدادات Space الخاص بك
2. انقر على "Settings" → "Repository secrets"
3. أضف المفاتيح التالية:

```
HF_TOKEN=your_huggingface_token_here
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
```

#### ب) للتطوير المحلي:
1. انسخ ملف `.env.example` إلى `.env`
2. املأ المفاتيح المطلوبة في ملف `.env`

### 2. المفاتيح المطلوبة

#### مفاتيح أساسية (مطلوبة):
- **HF_TOKEN**: مفتاح Hugging Face ([احصل عليه هنا](https://huggingface.co/settings/tokens))
- **GEMINI_API_KEY**: مفتاح Google Gemini ([احصل عليه هنا](https://makersuite.google.com/app/apikey))

#### مفاتيح اختيارية (لمميزات إضافية):
- **GOOGLE_SEARCH_ENGINE_ID**: لبحث Google المخصص
- **NEWSAPI_KEY**: لجمع الأخبار من NewsAPI
- **PEXELS_API_KEY**: للصور المجانية من Pexels

### 3. تشغيل التطبيق

#### على Hugging Face Spaces:
1. ارفع الملفات إلى Space
2. تأكد من إضافة المفاتيح في Repository secrets
3. سيبدأ التطبيق تلقائياً

#### محلياً:
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python app.py
```

## 🔧 التكوين المتقدم

### إعدادات البحث
```python
# في config/settings.py
SEARCH_INTERVAL_HOURS = 2  # البحث كل ساعتين
MAX_VIDEO_DURATION_MINUTES = 30  # حد أقصى 30 دقيقة للفيديوهات
```

### إعدادات الأمان
```python
# حدود API لتجنب تجاوز الحصص
GEMINI_RATE_LIMIT = 60  # طلبات في الدقيقة
BLOGGER_RATE_LIMIT = 100  # طلبات في اليوم
```

## 📁 هيكل المشروع

```
├── app.py                 # الواجهة الرئيسية (Gradio)
├── config/               # ملفات التكوين
│   ├── settings.py       # الإعدادات الرئيسية
│   └── api_config.py     # تكوين APIs
├── modules/              # الوحدات الأساسية
│   ├── content_generator.py
│   ├── advanced_youtube_analyzer.py
│   └── ...
├── requirements.txt      # المتطلبات
├── .env                 # متغيرات البيئة (لا يُرفع)
└── README.md            # هذا الملف
```

## 🛡️ الأمان والخصوصية

### حماية المفاتيح:
- ✅ استخدام متغيرات البيئة
- ✅ ملف `.gitignore` شامل
- ✅ عدم تخزين المفاتيح في الكود
- ✅ تشفير البيانات الحساسة

### أفضل الممارسات:
1. **لا تشارك ملف `.env`** أبداً
2. **استخدم مفاتيح منفصلة** للتطوير والإنتاج
3. **راقب استخدام APIs** لتجنب تجاوز الحدود
4. **احدث المفاتيح بانتظام** لضمان الأمان

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ "Invalid API Key":
```bash
# تأكد من صحة المفتاح
echo $HF_TOKEN
# أو في Python
import os
print(os.getenv('HF_TOKEN'))
```

#### خطأ "Rate Limit Exceeded":
- انتظر قليلاً قبل المحاولة مرة أخرى
- تحقق من حدود API في لوحة التحكم

#### مشاكل الاستيراد:
```bash
# تأكد من تثبيت جميع المتطلبات
pip install -r requirements.txt --upgrade
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `logs/bot.log`
2. تأكد من صحة المفاتيح
3. راجع وثائق APIs المستخدمة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**ملاحظة**: هذا المشروع مصمم للاستخدام التعليمي والشخصي. تأكد من الامتثال لشروط استخدام جميع APIs المستخدمة.

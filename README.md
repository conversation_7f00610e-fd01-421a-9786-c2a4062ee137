# 🎮 Gaming News Agent

وكيل ذكي لجمع ونشر أخبار الألعاب باستخدام الذكاء الاصطناعي.

## ✨ المميزات

- 🤖 ذكاء اصطناعي متقدم
- 🔍 بحث ذكي للأخبار
- 🎥 تحليل مقاطع YouTube
- 🖼️ إنشاء الصور
- 📱 واجهة ويب سهلة الاستخدام
- 🔒 أمان عالي للبيانات

## 🚀 الإعداد

### للاستخدام على Hugging Face Spaces:
1. أضف المفاتيح المطلوبة في Repository Secrets
2. راجع ملف `.env.example` لمعرفة المفاتيح المطلوبة

### للتطوير المحلي:
1. انسخ `.env.example` إلى `.env`
2. أضف مفاتيحك في ملف `.env`
3. شغل `python app.py`

## 🔑 المفاتيح المطلوبة

### أساسية:
- `HF_TOKEN` - مفتاح Hugging Face
- `GEMINI_API_KEY` - مفتاح Google Gemini

### اختيارية:
- `GOOGLE_SEARCH_ENGINE_ID` - للبحث المخصص
- `YOUTUBE_API_KEY` - لتحليل YouTube
- `NEWSAPI_KEY` - لجمع الأخبار
- `PEXELS_API_KEY` - للصور المجانية

راجع `.env.example` للقائمة الكاملة.

## 📁 هيكل المشروع

```
├── app.py                 # التطبيق الرئيسي
├── requirements.txt       # المتطلبات
├── config/               # ملفات التكوين
├── modules/              # الوحدات الأساسية
└── .env.example          # مثال متغيرات البيئة
```

## 🛡️ الأمان

- جميع المفاتيح محمية في متغيرات البيئة
- لا توجد بيانات حساسة في الكود
- تشفير البيانات المرسلة

## 📄 الترخيص

MIT License

---

**ملاحظة**: للاستخدام التعليمي والشخصي. تأكد من الامتثال لشروط استخدام جميع APIs.

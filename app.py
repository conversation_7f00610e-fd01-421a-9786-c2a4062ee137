# -*- coding: utf-8 -*-
"""
واجهة ويب مبسطة لوكيل أخبار الألعاب
مصممة للعمل على Hugging Face Spaces
"""

import gradio as gr
import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Optional

# إعداد متغيرات البيئة الافتراضية
os.environ.setdefault('GRADIO_SERVER_NAME', '0.0.0.0')
os.environ.setdefault('GRADIO_SERVER_PORT', '7860')

class SimpleGamingNewsAgent:
    """وكيل أخبار الألعاب المبسط"""
    
    def __init__(self):
        self.status = "جاهز للعمل"
        self.logs = []
        
    def add_log(self, message: str):
        """إضافة رسالة إلى السجل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs.append(log_entry)
        if len(self.logs) > 100:  # الاحتفاظ بآخر 100 رسالة فقط
            self.logs.pop(0)
        return log_entry
    
    def get_logs(self) -> str:
        """الحصول على السجلات"""
        return "\n".join(self.logs[-20:])  # آخر 20 رسالة
    
    def check_api_keys(self) -> Dict[str, str]:
        """فحص مفاتيح API"""
        keys_status = {}
        
        # فحص المفاتيح الأساسية
        hf_token = os.getenv('HF_TOKEN', '')
        gemini_key = os.getenv('GEMINI_API_KEY', '')
        
        keys_status['HF_TOKEN'] = '✅ موجود' if hf_token else '❌ مفقود'
        keys_status['GEMINI_API_KEY'] = '✅ موجود' if gemini_key else '❌ مفقود'
        
        # فحص المفاتيح الاختيارية
        optional_keys = [
            'GOOGLE_SEARCH_ENGINE_ID',
            'YOUTUBE_API_KEY',
            'NEWSAPI_KEY',
            'PEXELS_API_KEY'
        ]
        
        for key in optional_keys:
            value = os.getenv(key, '')
            keys_status[key] = '✅ موجود' if value else '⚪ اختياري'
        
        return keys_status
    
    def generate_sample_article(self, topic: str = "أخبار الألعاب") -> str:
        """إنشاء مقال تجريبي"""
        self.add_log(f"بدء إنشاء مقال عن: {topic}")
        
        sample_article = f"""
# 🎮 {topic} - آخر التحديثات

## المقدمة
مرحباً بكم في آخر أخبار عالم الألعاب! في هذا المقال سنستعرض أحدث التطورات في صناعة الألعاب.

## أبرز الأخبار

### 🔥 إصدارات جديدة
- لعبة جديدة مثيرة قادمة قريباً
- تحديثات مهمة للألعاب الشائعة
- إعلانات من أكبر الشركات

### 🏆 أحداث مهمة
- بطولات الرياضات الإلكترونية
- معارض الألعاب القادمة
- مؤتمرات المطورين

## الخلاصة
عالم الألعاب في تطور مستمر، وهناك دائماً شيء جديد ومثير في الأفق!

---
*تم إنشاء هذا المقال بواسطة وكيل أخبار الألعاب الذكي*
*التاريخ: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
        """
        
        self.add_log("تم إنشاء المقال بنجاح")
        return sample_article.strip()

# إنشاء كائن الوكيل
agent = SimpleGamingNewsAgent()

def create_interface():
    """إنشاء واجهة Gradio"""
    
    with gr.Blocks(
        title="🎮 Gaming News Agent",
        theme=gr.themes.Soft(),
        css="""
        .main-header { text-align: center; margin-bottom: 2rem; }
        .status-box { background: #f0f8ff; padding: 1rem; border-radius: 8px; }
        .keys-box { background: #f5f5f5; padding: 1rem; border-radius: 8px; }
        """
    ) as demo:
        
        # العنوان الرئيسي
        gr.HTML("""
        <div class="main-header">
            <h1>🎮 Gaming News Agent</h1>
            <p>وكيل أخبار الألعاب الذكي - مدعوم بالذكاء الاصطناعي</p>
        </div>
        """)
        
        with gr.Tabs():
            
            # تبويب الحالة
            with gr.Tab("📊 حالة النظام"):
                with gr.Row():
                    with gr.Column():
                        gr.HTML('<div class="status-box"><h3>🔍 فحص مفاتيح API</h3></div>')
                        keys_display = gr.JSON(label="حالة المفاتيح", value=agent.check_api_keys())
                        refresh_keys_btn = gr.Button("🔄 تحديث", variant="secondary")
                        
                    with gr.Column():
                        gr.HTML('<div class="status-box"><h3>📝 سجل النشاط</h3></div>')
                        logs_display = gr.Textbox(
                            label="السجلات",
                            value=agent.get_logs(),
                            lines=10,
                            max_lines=15,
                            interactive=False
                        )
                        refresh_logs_btn = gr.Button("🔄 تحديث السجل", variant="secondary")
            
            # تبويب إنشاء المحتوى
            with gr.Tab("✍️ إنشاء المحتوى"):
                with gr.Row():
                    with gr.Column():
                        topic_input = gr.Textbox(
                            label="موضوع المقال",
                            placeholder="أدخل موضوع المقال (مثل: أخبار PlayStation، ألعاب جديدة، إلخ)",
                            value="أخبار الألعاب الجديدة"
                        )
                        generate_btn = gr.Button("🚀 إنشاء مقال", variant="primary")
                        
                    with gr.Column():
                        article_output = gr.Textbox(
                            label="المقال المُنشأ",
                            lines=20,
                            max_lines=25,
                            interactive=False
                        )
            
            # تبويب المعلومات
            with gr.Tab("ℹ️ معلومات"):
                gr.Markdown("""
                ## 🎮 حول وكيل أخبار الألعاب
                
                هذا التطبيق هو وكيل ذكي لجمع ونشر أخبار الألعاب باستخدام الذكاء الاصطناعي.
                
                ### ✨ المميزات:
                - 🤖 **ذكاء اصطناعي متقدم**: يستخدم Gemini AI
                - 🔍 **بحث ذكي**: يجمع الأخبار من مصادر متعددة
                - 🎥 **تحليل YouTube**: يحلل مقاطع الفيديو
                - 🖼️ **إنشاء الصور**: ينشئ صور مناسبة للمقالات
                - 🔒 **آمان عالي**: حماية المفاتيح والبيانات
                
                ### 🔑 إعداد المفاتيح:
                لاستخدام جميع المميزات، أضف المفاتيح التالية في Repository Secrets:
                - `HF_TOKEN`: مفتاح Hugging Face
                - `GEMINI_API_KEY`: مفتاح Google Gemini
                - `GOOGLE_SEARCH_ENGINE_ID`: معرف محرك البحث
                - `YOUTUBE_API_KEY`: مفتاح YouTube API
                
                ### 📞 الدعم:
                إذا واجهت أي مشاكل، تحقق من سجل النشاط أو راجع الوثائق.
                """)
        
        # ربط الأحداث
        refresh_keys_btn.click(
            fn=lambda: agent.check_api_keys(),
            outputs=keys_display
        )
        
        refresh_logs_btn.click(
            fn=lambda: agent.get_logs(),
            outputs=logs_display
        )
        
        generate_btn.click(
            fn=agent.generate_sample_article,
            inputs=topic_input,
            outputs=article_output
        )
    
    return demo

if __name__ == "__main__":
    # إضافة رسالة بدء التشغيل
    agent.add_log("🚀 بدء تشغيل وكيل أخبار الألعاب")
    agent.add_log("✅ تم تحميل الواجهة بنجاح")
    
    # إنشاء وتشغيل الواجهة
    demo = create_interface()
    
    # تشغيل التطبيق
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        quiet=False
    )

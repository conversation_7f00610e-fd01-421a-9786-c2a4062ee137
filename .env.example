# ملف مثال لمتغيرات البيئة - Gaming News Agent
# انسخ هذا الملف إلى .env واملأ القيم الحقيقية

# ===========================================
# مفاتيح Hugging Face (مطلوب)
# ===========================================
# احصل على مفتاحك من: https://huggingface.co/settings/tokens
HF_TOKEN=hf_your_token_here

# ===========================================
# مفاتيح Google APIs (مطلوب)
# ===========================================
# احصل على مفاتيحك من: https://console.cloud.google.com/
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
YOUTUBE_API_KEY=your_youtube_api_key_here

# قائمة مفاتيح Google API (مفصولة بفواصل)
GOOGLE_API_KEYS_LIST=key1,key2,key3
GOOGLE_SEARCH_KEYS=key1,key2,key3
GOOGLE_ADDITIONAL_KEY=your_additional_google_key_here
PAGESPEED_INSIGHTS_API_KEY=your_pagespeed_key_here

# ===========================================
# مفاتيح Blogger (اختياري)
# ===========================================
BLOGGER_CLIENT_ID=your_blogger_client_id_here
BLOGGER_CLIENT_SECRET=your_blogger_client_secret_here
BLOGGER_BLOG_ID=your_blogger_blog_id_here
BLOGGER_CLIENT_SECRET_FILE=client_secret.json

# ===========================================
# مفاتيح APIs الأخبار (اختياري)
# ===========================================
NEWSAPI_KEY=your_newsapi_key_here
NEWSDATA_KEY=your_newsdata_key_here
THENEWSAPI_KEY=your_thenewsapi_key_here
GNEWS_KEY=your_gnews_key_here

# ===========================================
# مفاتيح APIs البحث (اختياري)
# ===========================================
BRAVE_SEARCH_KEY=your_brave_search_key_here
SERPAPI_KEY=your_serpapi_key_here
RAPIDAPI_KEY=your_rapidapi_key_here
BING_SEARCH_KEY=your_bing_search_key_here

# مفاتيح Tavily API
TAVILY_API_KEY_1=your_tavily_key_1_here
TAVILY_API_KEY_2=your_tavily_key_2_here

# مفاتيح Search1API
SEARCH1API_KEY_1=your_search1api_key_1_here
SEARCH1API_KEY_2=your_search1api_key_2_here
SEARCH1API_KEY_3=your_search1api_key_3_here

# ===========================================
# مفاتيح APIs الصور (اختياري)
# ===========================================
PEXELS_API_KEY=your_pexels_api_key_here
PIXABAY_API_KEY=your_pixabay_api_key_here
UNSPLASH_ACCESS_KEY=your_unsplash_key_here

# مفاتيح إنشاء الصور بالذكاء الاصطناعي
FREEPIK_API_KEY=your_freepik_api_key_here
FLUXAI_API_KEY=your_fluxai_api_key_here
LEONARDO_AI_API_KEY=your_leonardo_ai_key_here
MIDJOURNEY_API_KEY=your_midjourney_key_here

# ===========================================
# مفاتيح APIs الألعاب (اختياري)
# ===========================================
TWITCH_CLIENT_ID=your_twitch_client_id_here
TWITCH_CLIENT_SECRET=your_twitch_client_secret_here
RAWG_API_KEY=your_rawg_api_key_here
STEAM_API_KEY=your_steam_api_key_here

# ===========================================
# مفاتيح APIs تحويل النص إلى صوت (اختياري)
# ===========================================
ASSEMBLYAI_API_KEY=your_assemblyai_key_here
SPEECHMATICS_API_KEY=your_speechmatics_key_here
IBM_WATSON_API_KEY=your_ibm_watson_key_here
IBM_WATSON_URL=your_ibm_watson_url_here
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here
GOOGLE_CLOUD_SPEECH_KEY=your_google_cloud_speech_key_here
GOOGLE_CLOUD_SPEECH_PROJECT_ID=your_google_cloud_project_id_here
WITAI_ACCESS_TOKEN=your_witai_token_here

# ===========================================
# مفاتيح APIs تحميل الصوت (اختياري)
# ===========================================
APIFY_API_TOKEN=your_apify_token_here

# ===========================================
# مفاتيح النماذج الاحتياطية (اختياري)
# ===========================================
DEEPSEEK_API_KEY=your_deepseek_key_here
GROQ_API_KEY=your_groq_key_here

# ===========================================
# إعدادات الموقع
# ===========================================
WEBSITE_NAME=Gaming News

# ===========================================
# إعدادات Whisper
# ===========================================
WHISPER_API_URL=https://nanami34-ai55.hf.space/api/transcribe
WHISPER_API_KEY=whisper-hf-spaces-2025

# ===========================================
# إعدادات التشغيل
# ===========================================
MAX_VIDEO_DURATION_MINUTES=30
MAX_VIDEO_AGE_DAYS=60
APPROVAL_TIMEOUT_MINUTES=5
AUTO_APPROVE_ON_TIMEOUT=true

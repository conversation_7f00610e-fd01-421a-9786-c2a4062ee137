# متطلبات وكيل أخبار الألعاب - محسن للعمل على Hugging Face Spaces

# الذكاء الاصطناعي والمحتوى
google-generativeai
google-api-python-client
google-auth-oauthlib
google-auth

# واجهة Gradio للـ Hugging Face
gradio

# تيليجرام
python-telegram-bot
aiohttp

# استخراج المحتوى ومعالجة الويب
beautifulsoup4
requests
lxml
html5lib

# معالجة النصوص والتحليل
nltk
textblob
python-dateutil

# YouTube transcript extraction
youtube-transcript-api

# الصور والوسائط
Pillow
imageio

# الأدوات المساعدة
aiofiles
python-dotenv
schedule
pytz

# JSON والبيانات
ujson
pydantic

# التسجيل المتقدم
colorlog

# أدوات الشبكة
httpx

# معالجة الوقت والتاريخ
arrow

# أدوات التحليل الأساسية
pandas
numpy

# أدوات التحكم
click

# إنشاء الصور بالذكاء الاصطناعي
opencv-python-headless
matplotlib

# أدوات النظام الأساسية
psutil

# 🚀 دليل إعداد Gaming News Agent على Hugging Face Spaces

## 📋 الخطوات المطلوبة

### 1. إنشاء Space جديد

1. اذهب إلى [Hugging Face Spaces](https://huggingface.co/spaces)
2. انقر على "Create new Space"
3. اختر:
   - **Space name**: gaming-news-agent (أو أي اسم تفضله)
   - **License**: MIT
   - **Space SDK**: Gradio
   - **Space hardware**: CPU basic (مجاني)

### 2. إعداد Repository Secrets (مهم جداً!)

بعد إنشاء Space:

1. اذهب إلى **Settings** → **Repository secrets**
2. أضف المفاتيح التالية:

#### مفاتيح أساسية (مطلوبة):

```
HF_TOKEN=hf_your_actual_token_here
```
- احصل عليه من: https://huggingface.co/settings/tokens
- اختر "Write" permissions

```
GEMINI_API_KEY=your_actual_gemini_key_here
```
- احصل عليه من: https://makersuite.google.com/app/apikey

#### مفاتيح اختيارية (للمميزات الإضافية):

```
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
YOUTUBE_API_KEY=your_youtube_api_key
NEWSAPI_KEY=your_newsapi_key
PEXELS_API_KEY=your_pexels_key
```

### 3. رفع الملفات

ارفع جميع ملفات المشروع **عدا**:
- ❌ `.env` (لا ترفعه أبداً!)
- ❌ `client_secret.json` (إذا كان يحتوي على بيانات حقيقية)
- ❌ مجلدات `logs/`, `cache/`, `data/`

### 4. التحقق من الإعداد

بعد رفع الملفات:

1. انتظر حتى يبدأ Space في البناء
2. تحقق من logs البناء للتأكد من عدم وجود أخطاء
3. إذا ظهرت أخطاء متعلقة بالمفاتيح، تأكد من إضافتها في Repository secrets

## 🔧 إعدادات متقدمة

### تخصيص Space

في ملف `README.md` الخاص بـ Space، أضف:

```yaml
---
title: Gaming News Agent
emoji: 🎮
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---
```

### تحسين الأداء

1. **استخدام CPU basic**: كافي للمعظم
2. **تفعيل Auto-sleep**: لتوفير الموارد
3. **تحديد timeout**: في `app.py`

```python
demo.launch(
    server_name="0.0.0.0",
    server_port=7860,
    share=False,
    show_error=True,
    quiet=False
)
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ "HF_TOKEN not found"
**الحل**: تأكد من إضافة `HF_TOKEN` في Repository secrets

#### 2. خطأ "Invalid API Key"
**الحل**: تحقق من صحة المفاتيح في Repository secrets

#### 3. خطأ "Module not found"
**الحل**: تأكد من وجود جميع الملفات في `requirements.txt`

#### 4. Space لا يبدأ
**الحل**: 
- تحقق من logs البناء
- تأكد من وجود `app.py` في الجذر
- تحقق من syntax الكود

### فحص المفاتيح:

أضف هذا الكود في `app.py` للتحقق (احذفه بعد التأكد):

```python
import os
print("🔑 فحص المفاتيح:")
print(f"HF_TOKEN: {'✅ موجود' if os.getenv('HF_TOKEN') else '❌ مفقود'}")
print(f"GEMINI_API_KEY: {'✅ موجود' if os.getenv('GEMINI_API_KEY') else '❌ مفقود'}")
```

## 📊 مراقبة الأداء

### في لوحة التحكم:

1. **CPU Usage**: يجب أن يكون < 80%
2. **Memory Usage**: يجب أن يكون < 1GB
3. **API Calls**: راقب حدود APIs

### تحسين الاستخدام:

```python
# في config/settings.py
GEMINI_RATE_LIMIT = 30  # قلل إذا كان الاستخدام مرتفع
SEARCH_INTERVAL_HOURS = 4  # زد الفترة بين البحثات
```

## 🔒 أمان إضافي

### حماية المفاتيح:

1. **لا تطبع المفاتيح** في logs
2. **استخدم متغيرات البيئة** فقط
3. **احذف أي مفاتيح** من الكود

### مثال آمن:

```python
# ✅ صحيح
hf_token = os.getenv("HF_TOKEN", "")

# ❌ خطأ
hf_token = "*************************************"
```

## 📞 الدعم

إذا واجهت مشاكل:

1. **تحقق من logs** في Space
2. **راجع Repository secrets**
3. **تأكد من صحة المفاتيح**
4. **اقرأ وثائق Hugging Face Spaces**

## 🎉 بعد الإعداد الناجح

Space الخاص بك سيكون متاح على:
```
https://huggingface.co/spaces/YOUR_USERNAME/gaming-news-agent
```

ويمكن للآخرين استخدامه مباشرة دون الحاجة لإعداد محلي!

---

**تذكير مهم**: لا تشارك مفاتيح APIs الخاصة بك مع أي شخص!
